# Hướng dẫn kết nối L298N với ESP8266

## S<PERSON> đồ kết nối

### ESP8266 NodeMCU → L298N
```
ESP8266 Pin    →    L298<PERSON>n    →    <PERSON>ứ<PERSON> năng
D0 (GPIO16)    →    ENA          →    PWM Enable A (điề<PERSON> khiển tốc độ)
D3 (GPIO0)     →    IN1          →    Input 1 (đi<PERSON><PERSON> khiể<PERSON> hướng)
D4 (GPIO2)     →    IN2          →    Input 2 (điề<PERSON> khiển hướng)
GND            →    GND          →    Đ<PERSON>t chung
```

### L298N → Động cơ DC
```
L298N Pin      →    Động cơ DC
OUT1           →    Cực dương động cơ
OUT2           →    Cực âm động cơ
```

### Nguồn điện
```
L298N Pin      →    Nguồn
VCC (12V)      →    Nguồn 6-12V cho động cơ
5V             →    Nguồn 5V cho logic (c<PERSON> thể lấy từ ESP8266)
GND            →    <PERSON><PERSON>t chung với ESP8266
```

## Cấu hình trong code

### Định nghĩa chân GPIO:
```cpp
const int MOTOR_ENA = 16;  // GPIO16 (D0) - PWM Enable A
const int MOTOR_IN1 = 0;   // GPIO0 (D3) - Input 1  
const int MOTOR_IN2 = 2;   // GPIO2 (D4) - Input 2
```

### Thông số động cơ:
- Tốc độ: 0-100% (PWM)
- Hướng quay: 1 = thuận, -1 = nghịch
- Tốc độ tối thiểu: 30% (để động cơ có thể quay)

## Cách sử dụng

### Giao diện Web:
1. Truy cập http://waterlevel.local hoặc IP của ESP8266
2. Tìm card "Động Cơ DC" 
3. Điều chỉnh tốc độ bằng thanh trượt (0-100%)
4. Chọn hướng quay: Thuận (↻) hoặc Nghịch (↺)
5. Nhấn "BẬT ĐỘNG CƠ" để khởi động
6. Nhấn "TẮT ĐỘNG CƠ" để dừng

### API Commands:
```
GET /motor/on                    - Bật động cơ
GET /motor/off                   - Tắt động cơ
GET /motor/speed/[0-100]         - Thiết lập tốc độ (0-100%)
GET /motor/direction/[1|-1]      - Thiết lập hướng (1=thuận, -1=nghịch)
GET /api/status                  - Lấy trạng thái tổng thể
```

### Ví dụ API:
```
http://waterlevel.local/motor/speed/75    - Thiết lập tốc độ 75%
http://waterlevel.local/motor/direction/1 - Quay thuận
http://waterlevel.local/motor/on          - Bật động cơ
```

## Lưu ý an toàn

1. **Nguồn điện**: L298N cần nguồn riêng 6-12V cho động cơ
2. **Dòng điện**: Kiểm tra dòng tiêu thụ của động cơ không vượt quá 2A
3. **Tản nhiệt**: L298N có thể nóng khi hoạt động, cần tản nhiệt
4. **Đất chung**: Đảm bảo GND của ESP8266 và L298N được nối chung
5. **Tốc độ tối thiểu**: Dưới 30% động cơ có thể không quay được

## Troubleshooting

### Động cơ không quay:
- Kiểm tra kết nối dây
- Kiểm tra nguồn điện L298N
- Đảm bảo tốc độ ≥ 30%
- Kiểm tra GND chung

### Động cơ quay sai hướng:
- Đổi dây OUT1 và OUT2
- Hoặc sử dụng nút "NGHỊCH" trong giao diện

### ESP8266 reset khi bật động cơ:
- Nguồn ESP8266 không đủ mạnh
- Sử dụng nguồn riêng cho L298N
- Thêm tụ điện lọc nhiễu
