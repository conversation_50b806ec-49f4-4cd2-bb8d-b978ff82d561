<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Motor Control</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #0a0a0a; color: #fff; }
        .container { max-width: 600px; margin: 0 auto; }
        .test-section { background: #1a1a1a; padding: 20px; margin: 10px 0; border-radius: 8px; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-test { background: #3498db; color: white; }
        .btn-motor-on { background: #00ff88; color: black; }
        .btn-motor-off { background: #ff4757; color: white; }
        input[type="range"] { width: 100%; margin: 10px 0; }
        .log { background: #000; padding: 10px; border-radius: 5px; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .status { padding: 10px; background: #2a2a2a; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Motor Control</h1>
        
        <div class="test-section">
            <h3>📡 API Test</h3>
            <button class="btn-test" onclick="testAPI('/api/status')">Test /api/status</button>
            <button class="btn-test" onclick="testAPI('/motor/on')">Test /motor/on</button>
            <button class="btn-test" onclick="testAPI('/motor/off')">Test /motor/off</button>
            <button class="btn-test" onclick="testAPI('/motor/speed/50')">Test /motor/speed/50</button>
            <button class="btn-test" onclick="testAPI('/motor/direction/1')">Test /motor/direction/1</button>
        </div>

        <div class="test-section">
            <h3>⚙️ Motor Control</h3>
            <div>
                <label>Tốc độ: <span id="speed-display">50</span>%</label><br>
                <input type="range" id="speed-slider" min="0" max="100" value="50" 
                       oninput="updateSpeedDisplay(this.value)" onchange="setMotorSpeed(this.value)">
            </div>
            <div style="margin: 10px 0;">
                <button class="btn-motor-on" onclick="controlMotor('on')">BẬT ĐỘNG CƠ</button>
                <button class="btn-motor-off" onclick="controlMotor('off')">TẮT ĐỘNG CƠ</button>
            </div>
            <div>
                <button class="btn-test" onclick="setDirection(1)">THUẬN ↻</button>
                <button class="btn-test" onclick="setDirection(-1)">NGHỊCH ↺</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Status</h3>
            <div class="status" id="status-display">
                Nhấn "Refresh Status" để cập nhật...
            </div>
            <button class="btn-test" onclick="refreshStatus()">Refresh Status</button>
            <button class="btn-test" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📝 Debug Log</h3>
            <div class="log" id="debug-log"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        function testAPI(endpoint) {
            log(`🔄 Testing API: ${endpoint}`);
            fetch(endpoint)
                .then(response => {
                    log(`✅ Response status: ${response.status}`);
                    return response.text();
                })
                .then(data => {
                    log(`📄 Response data: ${data}`);
                })
                .catch(error => {
                    log(`❌ Error: ${error.message}`);
                });
        }

        function controlMotor(action) {
            log(`⚙️ Control motor: ${action}`);
            fetch('/motor/' + action)
                .then(response => response.text())
                .then(data => { 
                    log(`✅ Motor ${action}: ${data}`);
                    refreshStatus(); 
                })
                .catch(error => { 
                    log(`❌ Motor error: ${error.message}`);
                });
        }

        function setMotorSpeed(speed) {
            log(`🏃 Set speed: ${speed}%`);
            fetch('/motor/speed/' + speed)
                .then(response => response.text())
                .then(data => { 
                    log(`✅ Speed set: ${data}`);
                    refreshStatus(); 
                })
                .catch(error => { 
                    log(`❌ Speed error: ${error.message}`);
                });
        }

        function setDirection(direction) {
            const dirText = direction == 1 ? 'THUẬN' : 'NGHỊCH';
            log(`🔄 Set direction: ${dirText} (${direction})`);
            fetch('/motor/direction/' + direction)
                .then(response => response.text())
                .then(data => { 
                    log(`✅ Direction set: ${data}`);
                    refreshStatus(); 
                })
                .catch(error => { 
                    log(`❌ Direction error: ${error.message}`);
                });
        }

        function updateSpeedDisplay(value) {
            document.getElementById('speed-display').textContent = value;
        }

        function refreshStatus() {
            log('📊 Refreshing status...');
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('status-display');
                    statusDiv.innerHTML = `
                        <strong>Water Level:</strong> ${data.level} cm<br>
                        <strong>Pump:</strong> ${data.pump ? 'ON' : 'OFF'}<br>
                        <strong>Motor:</strong> ${data.motor ? 'ON' : 'OFF'}<br>
                        <strong>Motor Speed:</strong> ${data.motorSpeed}%<br>
                        <strong>Motor Direction:</strong> ${data.motorDirection == 1 ? 'THUẬN' : 'NGHỊCH'}
                    `;
                    log(`✅ Status updated: Motor=${data.motor}, Speed=${data.motorSpeed}%, Dir=${data.motorDirection}`);
                })
                .catch(error => {
                    log(`❌ Status error: ${error.message}`);
                });
        }

        // Auto refresh every 5 seconds
        setInterval(refreshStatus, 5000);
        
        // Initial status load
        setTimeout(refreshStatus, 1000);
        
        log('🚀 Debug page loaded');
    </script>
</body>
</html>
