#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <ESP8266mDNS.h>
#include <Wire.h>
#include <Adafruit_VL53L0X.h>

// ===== CẤU HÌNH WIFI =====
const char* ssid = "ABC";           // Thay đổi tên WiFi của bạn
const char* password = "88888888";  // Thay đổi mật khẩu WiFi của bạn

// ===== CẤU HÌNH ACCESS POINT =====
const char* ap_ssid = "ESP8266-WaterLevel";   // Tên WiFi của ESP khi làm AP
const char* ap_password = "12345678";         // Mật khẩu WiFi của ESP (tối thiểu 8 ký tự)

// ===== CẤU HÌNH AP NÂNG CAO =====
const int ap_channel = 6;                     // <PERSON><PERSON><PERSON> WiFi (1-13, tr<PERSON><PERSON> xung đột)
const int ap_max_connections = 4;             // Số kết nối tối đa
const bool ap_hidden = false;                 // Ẩn SSID hay không

// ===== CẤU HÌNH mDNS =====
const char* mdns_name = "waterlevel";         // Truy cập qua http://waterlevel.local

// ===== BIẾN TRẠNG THÁI =====
bool isWiFiConnected = false;       // Trạng thái kết nối WiFi
bool isAPMode = false;              // Trạng thái chế độ AP
unsigned long lastAPCheck = 0;      // Thời gian kiểm tra AP cuối cùng
unsigned long apStartTime = 0;      // Thời gian bắt đầu AP mode
unsigned long lastWiFiRetry = 0;    // Thời gian thử WiFi cuối cùng
bool wifiRetryInProgress = false;   // Đang thử kết nối WiFi

// Tạo web server trên port 80
ESP8266WebServer server(80);

// ===== CẤU HÌNH THIẾT BỊ =====
// Cảm biến laser VL53L0X (I2C)
// SCL -> D1 (GPIO5)
// SDA -> D2 (GPIO4)
Adafruit_VL53L0X vl53l0x = Adafruit_VL53L0X();

// Bơm nước
const int PUMP_PIN = 14;   // GPIO14 (D5 trên NodeMCU) - Điều khiển relay bơm

// Động cơ DC với L298N
const int MOTOR_ENA = 16;  // GPIO16 (D0 trên NodeMCU) - PWM Enable A
const int MOTOR_IN1 = 0;   // GPIO0 (D3 trên NodeMCU) - Input 1
const int MOTOR_IN2 = 2;   // GPIO2 (D4 trên NodeMCU) - Input 2

// Nút vật lý
const int BUTTON_PIN = 12; // GPIO12 (D6 trên NodeMCU) - Nút nhấn giữ

// ===== BIẾN TRẠNG THÁI =====
float waterLevel = 0.0;           // Mực nước hiện tại (cm)
bool pumpState = false;           // Trạng thái bơm (bật/tắt)
bool buttonPressed = false;       // Trạng thái nút nhấn
unsigned long buttonPressTime = 0; // Thời gian bắt đầu nhấn nút
unsigned long lastSensorRead = 0; // Thời gian đọc cảm biến cuối cùng
const unsigned long SENSOR_INTERVAL = 1000; // Đọc cảm biến mỗi 1 giây
const unsigned long BUTTON_HOLD_TIME = 500; // Thời gian nhấn giữ tối thiểu (ms)

// Biến trạng thái động cơ DC
bool motorState = false;          // Trạng thái động cơ (bật/tắt)
int motorSpeed = 0;               // Tốc độ động cơ (0-100%)
int motorDirection = 1;           // Hướng quay: 1=thuận, -1=nghịch
const int MIN_MOTOR_SPEED = 30;   // Tốc độ tối thiểu để động cơ quay (30%)
const int MAX_MOTOR_SPEED = 100;  // Tốc độ tối đa (100%)

// Cấu hình mực nước cho VL53L0X
const float TANK_HEIGHT = 100.0;  // Chiều cao bể chứa (cm)
const float MIN_DISTANCE = 3.0;   // Khoảng cách tối thiểu từ cảm biến đến mặt nước (cm)
const float MAX_DISTANCE = 200.0; // Khoảng cách tối đa từ cảm biến (cm) - VL53L0X max range

// Khai báo các hàm
void checkWiFiConnection();
void startStableAP();
void maintainAPConnection();
float readWaterLevel();
void controlPump(bool state);
void handleButtonPress();
void controlMotor(bool state, int speed, int direction);
void setMotorSpeed(int speed);
void setMotorDirection(int direction);
void handleDebugPage();

// Hàm tạo trang HTML với điều khiển hệ thống đo mực nước
String getHTMLPage() {
  String html = "<!DOCTYPE html>";
  html += "<html lang='vi'>";
  html += "<head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>Hệ Thống Đo Mực Nước</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #0a0a0a; color: #ffffff; }";
  html += ".container { max-width: 800px; margin: 0 auto; background: #1a1a1a; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.5); }";
  html += "h1 { color: #ffffff; text-align: center; margin-bottom: 30px; font-size: 28px; }";
  html += ".welcome { text-align: center; font-size: 16px; color: #b0b0b0; margin-bottom: 30px; }";
  html += ".devices { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }";
  html += ".device-card { background: #2a2a2a; border: 1px solid #404040; border-radius: 12px; padding: 20px; }";
  html += ".device-name { font-size: 18px; font-weight: bold; color: #ffffff; margin-bottom: 15px; display: flex; align-items: center; }";
  html += ".device-icon { margin-right: 10px; }";
  html += ".status-display { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; }";
  html += ".status-text { font-weight: bold; text-transform: uppercase; }";
  html += ".status-on { color: #00ff88; }";
  html += ".status-off { color: #808080; }";
  html += ".status-indicator { width: 10px; height: 10px; border-radius: 50%; }";
  html += ".indicator-on { background: #00ff88; box-shadow: 0 0 10px #00ff88; }";
  html += ".indicator-off { background: #808080; }";
  html += ".controls { display: flex; gap: 10px; flex-wrap: wrap; }";
  html += ".btn { flex: 1; min-width: 80px; padding: 12px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: bold; cursor: pointer; transition: all 0.3s; text-transform: uppercase; user-select: none; }";
  html += ".btn-on { background: #00ff88; color: #000; }";
  html += ".btn-on:hover { background: #00e67a; transform: translateY(-2px); }";
  html += ".btn-on:active { background: #00cc6a; transform: translateY(0px); box-shadow: 0 0 15px #00ff88; }";
  html += ".btn-off { background: #ff4757; color: #fff; }";
  html += ".btn-off:hover { background: #ff3742; transform: translateY(-2px); }";
  html += ".water-level { font-size: 24px; text-align: center; margin: 15px 0; color: #00ff88; }";
  html += ".water-tank { width: 100%; height: 200px; background: #1a1a1a; border: 2px solid #404040; border-radius: 8px; position: relative; margin: 15px 0; }";
  html += ".water-fill { background: linear-gradient(to top, #0066cc, #00aaff); position: absolute; bottom: 0; width: 100%; border-radius: 0 0 6px 6px; transition: height 0.5s ease; }";
  html += ".tank-labels { display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #b0b0b0; }";
  html += ".info { background: #2a2a2a; border: 1px solid #404040; padding: 20px; border-radius: 12px; }";
  html += ".info h3 { margin-top: 0; color: #00ff88; }";
  html += ".info p { margin: 8px 0; color: #b0b0b0; }";
  html += ".info strong { color: #ffffff; }";
  html += ".footer { text-align: center; margin-top: 30px; color: #808080; font-size: 14px; }";
  html += "input[type='range'] { -webkit-appearance: none; appearance: none; }";
  html += "input[type='range']::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 20px; height: 20px; border-radius: 50%; background: #00ff88; cursor: pointer; border: 2px solid #1a1a1a; }";
  html += "input[type='range']::-moz-range-thumb { width: 20px; height: 20px; border-radius: 50%; background: #00ff88; cursor: pointer; border: 2px solid #1a1a1a; }";
  html += "@media (max-width: 768px) { .devices { grid-template-columns: 1fr; } .controls { flex-direction: column; } }";
  html += "</style>";
  html += "</head>";
  html += "<body>";
  html += "<div class='container'>";
  html += "<h1>💧 Hệ Thống Đo Mực Nước</h1>";
  html += "<div class='welcome'>";
  html += "Giám sát và điều khiển mực nước tự động • Truy cập qua http://" + String(mdns_name) + ".local";
  html += "</div>";

  // Phần điều khiển thiết bị
  html += "<div class='devices'>";

  // Card hiển thị mực nước
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>📏</span>Mực Nước";
  html += "</div>";
  html += "<div class='water-level'>";
  html += String(waterLevel, 1) + " cm";
  html += "</div>";
  html += "<div class='water-tank'>";
  float waterPercentage = ((TANK_HEIGHT - waterLevel) / TANK_HEIGHT) * 100;
  if (waterPercentage > 100) waterPercentage = 100;
  if (waterPercentage < 0) waterPercentage = 0;
  html += "<div class='water-fill' style='height: " + String(waterPercentage) + "%;'></div>";
  html += "</div>";
  html += "<div class='tank-labels'>";
  html += "<span>Trống</span>";
  html += "<span>Đầy</span>";
  html += "</div>";
  html += "</div>";

  // Card điều khiển bơm nước
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>🚰</span>Bơm Nước";
  html += "</div>";
  html += "<div class='status-display'>";
  html += "<span class='status-text " + String(pumpState ? "status-on" : "status-off") + "'>" + String(pumpState ? "ĐANG BƠM" : "DỪNG") + "</span>";
  html += "<div class='status-indicator " + String(pumpState ? "indicator-on" : "indicator-off") + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onmousedown='startPump()' onmouseup='stopPump()' ontouchstart='startPump()' ontouchend='stopPump()'>NHẤN GIỮ ĐỂ BƠM</button>";
  html += "<button class='btn btn-off' onclick='controlPump(\"off\")'>TẮT BƠM</button>";
  html += "</div>";
  html += "<div style='margin-top: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; font-size: 14px; color: #b0b0b0;'>";
  html += "💡 <strong>Cách sử dụng:</strong><br>";
  html += "• Nhấn giữ nút \"NHẤN GIỮ ĐỂ BƠM\" để bơm nước<br>";
  html += "• Nhả nút để ngừng bơm<br>";
  html += "• Hoặc nhấn giữ nút vật lý trên thiết bị<br>";
  html += "• <strong>Không giới hạn thời gian bơm</strong>";
  html += "</div>";
  html += "</div>";

  // Card điều khiển động cơ DC
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>⚙️</span>Động Cơ DC";
  html += "</div>";
  html += "<div class='status-display'>";
  html += "<span class='status-text " + String(motorState ? "status-on" : "status-off") + "'>";
  html += motorState ? ("ĐANG QUAY " + String(motorSpeed) + "%") : "DỪNG";
  html += "</span>";
  html += "<div class='status-indicator " + String(motorState ? "indicator-on" : "indicator-off") + "'></div>";
  html += "</div>";
  html += "<div style='margin: 15px 0;'>";
  html += "<label style='color: #b0b0b0; font-size: 14px; display: block; margin-bottom: 8px;'>Tốc độ: <span id='speed-value'>" + String(motorSpeed) + "%</span></label>";
  html += "<input type='range' id='speed-slider' min='0' max='100' value='" + String(motorSpeed) + "' ";
  html += "style='width: 100%; height: 6px; background: #404040; border-radius: 3px; outline: none; -webkit-appearance: none;' ";
  html += "oninput='updateSpeedDisplay(this.value)' onchange='setMotorSpeed(this.value)'>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlMotor(\"on\")'>BẬT ĐỘNG CƠ</button>";
  html += "<button class='btn btn-off' onclick='controlMotor(\"off\")'>TẮT ĐỘNG CƠ</button>";
  html += "</div>";
  html += "<div class='controls' style='margin-top: 10px;'>";
  html += "<button class='btn' style='background: #3498db; color: #fff; flex: 1;' onclick='setDirection(1)'>THUẬN ↻</button>";
  html += "<button class='btn' style='background: #e74c3c; color: #fff; flex: 1;' onclick='setDirection(-1)'>NGHỊCH ↺</button>";
  html += "</div>";
  html += "<div style='margin-top: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; font-size: 14px; color: #b0b0b0;'>";
  html += "⚙️ <strong>Hướng dẫn:</strong><br>";
  html += "• Điều chỉnh tốc độ bằng thanh trượt (0-100%)<br>";
  html += "• Chọn hướng quay: Thuận hoặc Nghịch<br>";
  html += "• Tốc độ tối thiểu: " + String(MIN_MOTOR_SPEED) + "% để động cơ có thể quay<br>";
  html += "• Hướng hiện tại: " + String(motorDirection == 1 ? "Thuận ↻" : "Nghịch ↺");
  html += "</div>";
  html += "</div>";
  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>📡 Thông tin kết nối</h3>";

  if (isWiFiConnected) {
    html += "<p><strong>Chế độ:</strong> <span class='status connected'>WiFi Client</span></p>";
    html += "<p><strong>SSID:</strong> " + String(ssid) + "</p>";
    html += "<p><strong>Địa chỉ IP:</strong> " + WiFi.localIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Gateway:</strong> " + WiFi.gatewayIP().toString() + "</p>";
    html += "<p><strong>Cường độ tín hiệu:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
  } else if (isAPMode) {
    html += "<p><strong>Chế độ:</strong> <span class='status' style='background: #ffa502; color: #000;'>Access Point</span></p>";
    html += "<p><strong>AP SSID:</strong> " + String(ap_ssid) + "</p>";
    html += "<p><strong>AP IP:</strong> " + WiFi.softAPIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Clients kết nối:</strong> " + String(WiFi.softAPgetStationNum()) + "</p>";
    html += "<p><strong>Trạng thái:</strong> Chờ kết nối WiFi...</p>";
  }

  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>⚡ Thông tin hệ thống</h3>";
  html += "<p><strong>Thời gian hoạt động:</strong> " + String(millis() / 1000) + " giây</p>";
  html += "<p><strong>Bộ nhớ trống:</strong> " + String(ESP.getFreeHeap()) + " bytes</p>";
  html += "<p><strong>Tần số CPU:</strong> " + String(ESP.getCpuFreqMHz()) + " MHz</p>";
  html += "</div>";
  
  html += "<div class='footer'>";
  html += "Hệ Thống Đo Mực Nước ESP8266<br>";
  html += "Truy cập qua: <strong>http://" + String(mdns_name) + ".local</strong>";
  html += "</div>";
  html += "</div>";

  // JavaScript
  html += "<script>";
  html += "let pumpInterval = null;";
  html += "let isPumping = false;";
  html += "";
  html += "function controlPump(action) {";
  html += "  fetch('/pump/' + action)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { updatePumpStatus(); })";
  html += "    .catch(error => { console.error('Lỗi:', error); alert('Không thể kết nối!'); });";
  html += "}";
  html += "";
  html += "function startPump() {";
  html += "  if (!isPumping) {";
  html += "    isPumping = true;";
  html += "    controlPump('on');";
  html += "    console.log('Bắt đầu bơm nước');";
  html += "  }";
  html += "}";
  html += "";
  html += "function stopPump() {";
  html += "  if (isPumping) {";
  html += "    isPumping = false;";
  html += "    controlPump('off');";
  html += "    console.log('Ngừng bơm nước');";
  html += "  }";
  html += "}";
  html += "";
  html += "function controlMotor(action) {";
  html += "  fetch('/motor/' + action)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { updateStatus(); })";
  html += "    .catch(error => { console.error('Lỗi động cơ:', error); alert('Không thể điều khiển động cơ!'); });";
  html += "}";
  html += "";
  html += "function setMotorSpeed(speed) {";
  html += "  fetch('/motor/speed/' + speed)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { updateStatus(); })";
  html += "    .catch(error => { console.error('Lỗi tốc độ:', error); });";
  html += "}";
  html += "";
  html += "function setDirection(direction) {";
  html += "  fetch('/motor/direction/' + direction)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { updateStatus(); })";
  html += "    .catch(error => { console.error('Lỗi hướng quay:', error); });";
  html += "}";
  html += "";
  html += "function updateSpeedDisplay(value) {";
  html += "  document.getElementById('speed-value').textContent = value + '%';";
  html += "}";
  html += "";
  html += "function updatePumpStatus() {";
  html += "  fetch('/api/waterlevel')";
  html += "    .then(response => response.json())";
  html += "    .then(data => {";
  html += "      // Cập nhật trạng thái bơm";
  html += "      const statusText = document.querySelector('.status-text');";
  html += "      const statusIndicator = document.querySelector('.status-indicator');";
  html += "      if (data.pump) {";
  html += "        statusText.textContent = 'ĐANG BƠM';";
  html += "        statusText.className = 'status-text status-on';";
  html += "        statusIndicator.className = 'status-indicator indicator-on';";
  html += "      } else {";
  html += "        statusText.textContent = 'DỪNG';";
  html += "        statusText.className = 'status-text status-off';";
  html += "        statusIndicator.className = 'status-indicator indicator-off';";
  html += "      }";
  html += "    })";
  html += "    .catch(error => console.error('Lỗi cập nhật trạng thái bơm:', error));";
  html += "}";
  html += "";
  html += "function updateStatus() {";
  html += "  fetch('/api/status')";
  html += "    .then(response => response.json())";
  html += "    .then(data => {";
  html += "      // Cập nhật mực nước";
  html += "      document.querySelector('.water-level').textContent = data.level.toFixed(1) + ' cm';";
  html += "      let percentage = ((100 - data.level) / 100) * 100;";
  html += "      if (percentage > 100) percentage = 100;";
  html += "      if (percentage < 0) percentage = 0;";
  html += "      document.querySelector('.water-fill').style.height = percentage + '%';";
  html += "      ";
  html += "      // Cập nhật trạng thái bơm";
  html += "      const pumpStatusTexts = document.querySelectorAll('.status-text');";
  html += "      const pumpStatusIndicators = document.querySelectorAll('.status-indicator');";
  html += "      if (data.pump) {";
  html += "        pumpStatusTexts[0].textContent = 'ĐANG BƠM';";
  html += "        pumpStatusTexts[0].className = 'status-text status-on';";
  html += "        pumpStatusIndicators[0].className = 'status-indicator indicator-on';";
  html += "      } else {";
  html += "        pumpStatusTexts[0].textContent = 'DỪNG';";
  html += "        pumpStatusTexts[0].className = 'status-text status-off';";
  html += "        pumpStatusIndicators[0].className = 'status-indicator indicator-off';";
  html += "      }";
  html += "      ";
  html += "      // Cập nhật trạng thái động cơ";
  html += "      if (data.motor) {";
  html += "        pumpStatusTexts[1].textContent = 'ĐANG QUAY ' + data.motorSpeed + '%';";
  html += "        pumpStatusTexts[1].className = 'status-text status-on';";
  html += "        pumpStatusIndicators[1].className = 'status-indicator indicator-on';";
  html += "      } else {";
  html += "        pumpStatusTexts[1].textContent = 'DỪNG';";
  html += "        pumpStatusTexts[1].className = 'status-text status-off';";
  html += "        pumpStatusIndicators[1].className = 'status-indicator indicator-off';";
  html += "      }";
  html += "      ";
  html += "      // Cập nhật slider tốc độ";
  html += "      document.getElementById('speed-slider').value = data.motorSpeed;";
  html += "      document.getElementById('speed-value').textContent = data.motorSpeed + '%';";
  html += "    })";
  html += "    .catch(error => console.error('Lỗi cập nhật:', error));";
  html += "}";
  html += "";
  html += "// Tự động cập nhật trạng thái mỗi 2 giây";
  html += "setInterval(updateStatus, 2000);";
  html += "";
  html += "// Đã bỏ chế độ tự động tắt bơm khi rời khỏi trang";
  html += "// Người dùng có thể bơm liên tục mà không bị gián đoạn";
  html += "</script>";
  html += "</body>";
  html += "</html>";

  return html;
}

// Hàm đọc mực nước từ cảm biến VL53L0X
float readWaterLevel() {
  VL53L0X_RangingMeasurementData_t measure;
  vl53l0x.rangingTest(&measure, false);

  // Chỉ chấp nhận status 0 (Good) hoặc 1 (Sigma Fail)
  if (measure.RangeStatus == 0 || measure.RangeStatus == 1) {
    float distance = measure.RangeMilliMeter / 10.0; // mm → cm

    // Kiểm tra khoảng cách hợp lệ
    if (distance < MIN_DISTANCE || distance > 120.0) {
      return -1; // Ngoài tầm đo
    }

    // Tính mực nước
    float level = TANK_HEIGHT - distance;
    if (level < 0) level = 0;
    if (level > TANK_HEIGHT) level = TANK_HEIGHT;

    return level;
  }

  return -1; // Lỗi đọc cảm biến
}

// Hàm điều khiển bơm nước
void controlPump(bool state) {
  pumpState = state;
  digitalWrite(PUMP_PIN, state ? HIGH : LOW);
  Serial.println("🚰 Bơm: " + String(state ? "BẬT" : "TẮT"));
}

// Hàm điều khiển động cơ DC
void controlMotor(bool state, int speed, int direction) {
  Serial.println("🔧 controlMotor called: state=" + String(state) + ", speed=" + String(speed) + ", direction=" + String(direction));

  motorState = state;

  if (state) {
    // Đảm bảo tốc độ trong khoảng hợp lệ
    if (speed < MIN_MOTOR_SPEED) {
      Serial.println("🔧 Speed too low, adjusting from " + String(speed) + " to " + String(MIN_MOTOR_SPEED));
      speed = MIN_MOTOR_SPEED;
    }
    if (speed > MAX_MOTOR_SPEED) {
      Serial.println("🔧 Speed too high, adjusting from " + String(speed) + " to " + String(MAX_MOTOR_SPEED));
      speed = MAX_MOTOR_SPEED;
    }

    motorSpeed = speed;
    motorDirection = direction;

    // Chuyển đổi tốc độ từ % sang PWM (0-1023)
    int pwmValue = map(speed, 0, 100, 0, 1023);
    Serial.println("🔧 PWM value: " + String(pwmValue) + " (from " + String(speed) + "%)");

    // Thiết lập hướng quay
    if (direction == 1) {
      // Quay thuận
      digitalWrite(MOTOR_IN1, HIGH);
      digitalWrite(MOTOR_IN2, LOW);
      Serial.println("🔧 Direction: FORWARD (IN1=HIGH, IN2=LOW)");
    } else {
      // Quay nghịch
      digitalWrite(MOTOR_IN1, LOW);
      digitalWrite(MOTOR_IN2, HIGH);
      Serial.println("🔧 Direction: REVERSE (IN1=LOW, IN2=HIGH)");
    }

    // Thiết lập tốc độ PWM
    analogWrite(MOTOR_ENA, pwmValue);
    Serial.println("🔧 PWM written to pin " + String(MOTOR_ENA));

    Serial.println("⚙️ Động cơ: BẬT - Tốc độ: " + String(speed) + "% - Hướng: " +
                   String(direction == 1 ? "Thuận" : "Nghịch"));
  } else {
    // Tắt động cơ
    motorSpeed = 0;
    digitalWrite(MOTOR_IN1, LOW);
    digitalWrite(MOTOR_IN2, LOW);
    analogWrite(MOTOR_ENA, 0);

    Serial.println("⚙️ Động cơ: TẮT");
  }
}

// Hàm thiết lập tốc độ động cơ
void setMotorSpeed(int speed) {
  Serial.println("🔧 setMotorSpeed called: " + String(speed) + "% (current motorState=" + String(motorState) + ")");

  if (speed < 0) speed = 0;
  if (speed > MAX_MOTOR_SPEED) speed = MAX_MOTOR_SPEED;

  motorSpeed = speed;

  if (motorState && speed >= MIN_MOTOR_SPEED) {
    // Cập nhật tốc độ nếu động cơ đang chạy
    int pwmValue = map(speed, 0, 100, 0, 1023);
    analogWrite(MOTOR_ENA, pwmValue);
    Serial.println("⚙️ Tốc độ động cơ: " + String(speed) + "% (PWM=" + String(pwmValue) + ")");
  } else if (motorState && speed < MIN_MOTOR_SPEED) {
    // Tắt động cơ nếu tốc độ quá thấp
    Serial.println("🔧 Speed too low, stopping motor");
    controlMotor(false, 0, motorDirection);
  } else if (!motorState) {
    Serial.println("🔧 Motor not running, speed stored for next start");
  }
}

// Hàm thiết lập hướng quay động cơ
void setMotorDirection(int direction) {
  Serial.println("🔧 setMotorDirection called: " + String(direction) + " (current motorState=" + String(motorState) + ")");

  motorDirection = direction;

  if (motorState) {
    // Cập nhật hướng quay nếu động cơ đang chạy
    if (direction == 1) {
      digitalWrite(MOTOR_IN1, HIGH);
      digitalWrite(MOTOR_IN2, LOW);
      Serial.println("🔧 Direction pins set: IN1=HIGH, IN2=LOW");
    } else {
      digitalWrite(MOTOR_IN1, LOW);
      digitalWrite(MOTOR_IN2, HIGH);
      Serial.println("🔧 Direction pins set: IN1=LOW, IN2=HIGH");
    }
    Serial.println("⚙️ Hướng động cơ: " + String(direction == 1 ? "Thuận" : "Nghịch"));
  } else {
    Serial.println("🔧 Motor not running, direction stored for next start");
  }
}

// Hàm xử lý nút nhấn vật lý với debounce
void handleButtonPress() {
  static unsigned long lastDebounceTime = 0;
  static bool lastButtonState = HIGH;
  static bool stableButtonState = HIGH;
  const unsigned long debounceDelay = 50;

  bool currentButtonState = digitalRead(BUTTON_PIN);

  if (currentButtonState != lastButtonState) {
    lastDebounceTime = millis();
  }

  if ((millis() - lastDebounceTime) > debounceDelay) {
    if (currentButtonState != stableButtonState) {
      stableButtonState = currentButtonState;

      if (stableButtonState == HIGH && !buttonPressed) {
        buttonPressed = true;
        buttonPressTime = millis();
        controlPump(true);
        Serial.println("🔘 Nhấn giữ → BƠM");
      } else if (stableButtonState == LOW && buttonPressed) {
        buttonPressed = false;
        controlPump(false);
        Serial.println("🔘 Nhả nút → DỪNG");
      }
    }
  }

  lastButtonState = currentButtonState;
}

// Xử lý trang chủ
void handleRoot() {
  server.send(200, "text/html", getHTMLPage());
}

// Xử lý trang debug
void handleDebugPage() {
  String html = "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Debug Motor</title>";
  html += "<style>body{font-family:Arial;margin:20px;background:#0a0a0a;color:#fff;}";
  html += ".btn{padding:10px;margin:5px;border:none;border-radius:5px;cursor:pointer;color:#fff;}";
  html += ".btn-test{background:#3498db;}.btn-on{background:#00ff88;color:#000;}.btn-off{background:#ff4757;}";
  html += ".log{background:#000;padding:10px;height:300px;overflow-y:scroll;font-family:monospace;font-size:12px;}";
  html += "</style></head><body>";
  html += "<h1>🔧 Debug Motor Control</h1>";
  html += "<div><h3>API Tests</h3>";
  html += "<button class='btn btn-test' onclick='testAPI(\"/api/status\")'>Test Status</button>";
  html += "<button class='btn btn-on' onclick='testAPI(\"/motor/on\")'>Motor ON</button>";
  html += "<button class='btn btn-off' onclick='testAPI(\"/motor/off\")'>Motor OFF</button>";
  html += "<button class='btn btn-test' onclick='testAPI(\"/motor/speed/50\")'>Speed 50%</button>";
  html += "<button class='btn btn-test' onclick='testAPI(\"/motor/direction/1\")'>Forward</button>";
  html += "<button class='btn btn-test' onclick='clearLog()'>Clear Log</button>";
  html += "</div><div><h3>Debug Log</h3><div class='log' id='log'></div></div>";
  html += "<script>";
  html += "function log(msg){document.getElementById('log').innerHTML+=new Date().toLocaleTimeString()+': '+msg+'\\n';document.getElementById('log').scrollTop=999999;}";
  html += "function clearLog(){document.getElementById('log').innerHTML='';}";
  html += "function testAPI(url){log('Testing: '+url);fetch(url).then(r=>r.text()).then(d=>log('Response: '+d)).catch(e=>log('Error: '+e));}";
  html += "log('Debug page loaded');";
  html += "</script></body></html>";
  server.send(200, "text/html", html);
}

// Xử lý bật bơm nước
void handlePumpOn() {
  controlPump(true);
  server.send(200, "text/plain", "PUMP ON");
}

// Xử lý tắt bơm nước
void handlePumpOff() {
  controlPump(false);
  server.send(200, "text/plain", "PUMP OFF");
}

// API trả về mực nước hiện tại (JSON) - Deprecated, sử dụng handleStatusAPI
void handleWaterLevelAPI() {
  String json = "{\"level\":" + String(waterLevel, 2) + ",\"pump\":" + String(pumpState ? "true" : "false") + "}";
  server.send(200, "application/json", json);
}

// API trả về trạng thái tổng thể (JSON)
void handleStatusAPI() {
  String json = "{";
  json += "\"level\":" + String(waterLevel, 2) + ",";
  json += "\"pump\":" + String(pumpState ? "true" : "false") + ",";
  json += "\"motor\":" + String(motorState ? "true" : "false") + ",";
  json += "\"motorSpeed\":" + String(motorSpeed) + ",";
  json += "\"motorDirection\":" + String(motorDirection);
  json += "}";
  server.send(200, "application/json", json);
}

// Xử lý bật động cơ
void handleMotorOn() {
  Serial.println("🔧 API: /motor/on called");
  int useSpeed = motorSpeed > 0 ? motorSpeed : MIN_MOTOR_SPEED;
  Serial.println("🔧 Using speed: " + String(useSpeed) + "%, direction: " + String(motorDirection));
  controlMotor(true, useSpeed, motorDirection);
  server.send(200, "text/plain", "MOTOR ON - Speed: " + String(useSpeed) + "%, Direction: " + String(motorDirection));
}

// Xử lý tắt động cơ
void handleMotorOff() {
  Serial.println("🔧 API: /motor/off called");
  controlMotor(false, 0, motorDirection);
  server.send(200, "text/plain", "MOTOR OFF");
}

// Xử lý thiết lập tốc độ động cơ
void handleMotorSpeed() {
  if (server.hasArg("speed")) {
    int speed = server.arg("speed").toInt();
    setMotorSpeed(speed);
    server.send(200, "text/plain", "SPEED SET: " + String(speed));
  } else {
    server.send(400, "text/plain", "Missing speed parameter");
  }
}

// Xử lý thiết lập hướng động cơ
void handleMotorDirection() {
  if (server.hasArg("direction")) {
    int direction = server.arg("direction").toInt();
    if (direction == 1 || direction == -1) {
      setMotorDirection(direction);
      server.send(200, "text/plain", "DIRECTION SET: " + String(direction == 1 ? "FORWARD" : "REVERSE"));
    } else {
      server.send(400, "text/plain", "Invalid direction (use 1 or -1)");
    }
  } else {
    server.send(400, "text/plain", "Missing direction parameter");
  }
}

// Xử lý trang không tìm thấy và routes động
void handleNotFound() {
  String uri = server.uri();

  // Xử lý routes động cho tốc độ động cơ
  if (uri.startsWith("/motor/speed/")) {
    String speedStr = uri.substring(13); // Lấy phần sau "/motor/speed/"
    int speed = speedStr.toInt();
    Serial.println("🔧 API: " + uri + " -> speed=" + String(speed));

    if (speed >= 0 && speed <= 100) {
      setMotorSpeed(speed);
      server.send(200, "text/plain", "SPEED SET: " + String(speed) + "%");
      return;
    } else {
      Serial.println("❌ Invalid speed: " + String(speed));
      server.send(400, "text/plain", "Invalid speed: " + String(speed));
      return;
    }
  }

  // Xử lý routes động cho hướng động cơ
  if (uri.startsWith("/motor/direction/")) {
    String dirStr = uri.substring(17); // Lấy phần sau "/motor/direction/"
    int direction = dirStr.toInt();
    Serial.println("🔧 API: " + uri + " -> direction=" + String(direction));

    if (direction == 1 || direction == -1) {
      setMotorDirection(direction);
      server.send(200, "text/plain", "DIRECTION SET: " + String(direction == 1 ? "FORWARD" : "REVERSE"));
      return;
    } else {
      Serial.println("❌ Invalid direction: " + String(direction));
      server.send(400, "text/plain", "Invalid direction: " + String(direction));
      return;
    }
  }

  // Trang không tìm thấy thực sự
  String message = "Trang không tìm thấy!\n\n";
  message += "URI: " + uri + "\n";
  message += "Method: " + String(server.method()) + "\n";
  message += "Arguments: " + String(server.args()) + "\n";

  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }

  message += "\nAPI có sẵn:\n";
  message += "- /motor/on - Bật động cơ\n";
  message += "- /motor/off - Tắt động cơ\n";
  message += "- /motor/speed/[0-100] - Thiết lập tốc độ\n";
  message += "- /motor/direction/[1|-1] - Thiết lập hướng (1=thuận, -1=nghịch)\n";
  message += "- /api/status - Trạng thái tổng thể\n";

  server.send(404, "text/plain", message);
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  delay(100);
  Serial.println();
  Serial.println("========================================");
  Serial.println("💧 Hệ Thống Đo Mực Nước ESP8266");
  Serial.println("========================================");

  // Khởi tạo I2C và VL53L0X
  Wire.begin();
  Serial.print("🔧 Khởi tạo VL53L0X...");
  if (!vl53l0x.begin()) {
    Serial.println(" ❌ LỖI!");
  } else {
    Serial.println(" ✅ OK");
    vl53l0x.configSensor(Adafruit_VL53L0X::VL53L0X_SENSE_HIGH_ACCURACY);
  }

  // Cấu hình GPIO
  Serial.println("🔧 Configuring GPIO pins...");
  pinMode(PUMP_PIN, OUTPUT);
  pinMode(BUTTON_PIN, INPUT_PULLUP);
  pinMode(MOTOR_ENA, OUTPUT);
  pinMode(MOTOR_IN1, OUTPUT);
  pinMode(MOTOR_IN2, OUTPUT);

  Serial.println("🔧 Setting initial pin states...");
  digitalWrite(PUMP_PIN, LOW);
  digitalWrite(MOTOR_IN1, LOW);
  digitalWrite(MOTOR_IN2, LOW);
  analogWrite(MOTOR_ENA, 0);

  pumpState = false;
  motorState = false;
  motorSpeed = 0;
  motorDirection = 1; // Mặc định quay thuận

  Serial.println("✅ GPIO khởi tạo: Bơm=TẮT, Động cơ=TẮT, Nút=INPUT_PULLUP");
  Serial.println("🔧 Motor pins: ENA=" + String(MOTOR_ENA) + ", IN1=" + String(MOTOR_IN1) + ", IN2=" + String(MOTOR_IN2));

  // Test cảm biến một lần
  Serial.print("🧪 Test VL53L0X: ");
  float testLevel = readWaterLevel();
  Serial.println(testLevel >= 0 ? "✅ OK" : "❌ LỖI");

  // Thử kết nối WiFi
  Serial.print("📡 Đang thử kết nối WiFi: ");
  Serial.println(ssid);

  WiFi.mode(WIFI_STA);  // Chế độ Station (client)
  WiFi.begin(ssid, password);

  // Chờ kết nối WiFi trong 20 giây
  int attempts = 0;
  Serial.print("⏳ Đang kết nối");
  while (WiFi.status() != WL_CONNECTED && attempts < 40) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Hiển thị tiến trình
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.print("🔄 Thử lần " + String(attempts/10) + "/4");
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    // Kết nối WiFi thành công
    isWiFiConnected = true;
    Serial.println();
    Serial.println("✅ WiFi đã kết nối thành công!");
    Serial.println("========================================");
    Serial.println("📊 THÔNG TIN MẠNG:");
    Serial.print("🌐 Địa chỉ IP: ");
    Serial.println(WiFi.localIP());
    Serial.print("🚪 Gateway: ");
    Serial.println(WiFi.gatewayIP());
    Serial.print("📶 Cường độ tín hiệu: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.println("========================================");

    // Khởi tạo mDNS
    Serial.print("🔧 Đang cấu hình mDNS: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS!");
    }

  } else {
    // Không thể kết nối WiFi, chuyển sang chế độ AP
    Serial.println();
    Serial.println("❌ KHÔNG THỂ KẾT NỐI WIFI!");
    Serial.println("🔧 Nguyên nhân có thể:");
    Serial.println("   - Sai tên WiFi hoặc mật khẩu");
    Serial.println("   - WiFi không khả dụng");
    Serial.println("   - ESP8266 quá xa router");
    Serial.println();
    Serial.println("🔄 Chuyển sang chế độ Access Point...");

    // Chuyển sang chế độ AP với cấu hình ổn định
    startStableAP();
    isAPMode = true;
    apStartTime = millis();

    Serial.println("✅ Access Point đã được tạo!");
    Serial.println("========================================");
    Serial.println("📶 THÔNG TIN ACCESS POINT:");
    Serial.print("🏷️  SSID: ");
    Serial.println(ap_ssid);
    Serial.print("🔐 Password: ");
    Serial.println(ap_password);
    Serial.print("🌐 IP Address: ");
    Serial.println(WiFi.softAPIP());

    // Khởi tạo mDNS cho AP mode
    Serial.print("🔧 Đang cấu hình mDNS cho AP: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công cho AP mode!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký cho AP");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS cho AP mode!");
    }

    Serial.println("========================================");
    Serial.println("📱 HƯỚNG DẪN KẾT NỐI:");
    Serial.println("   1. Kết nối điện thoại/máy tính vào WiFi: " + String(ap_ssid));
    Serial.println("   2. Nhập mật khẩu: " + String(ap_password));
    Serial.println("   3. Truy cập bằng một trong các cách:");
    Serial.println("      • http://" + WiFi.softAPIP().toString());
    Serial.println("      • http://" + String(mdns_name) + ".local");
    Serial.println("   4. ESP8266 sẽ tự động thử kết nối WiFi lại sau 30 giây");
    Serial.println("========================================");
  }

  // Cấu hình web server (cho cả WiFi và AP mode)
  server.on("/", handleRoot);
  server.on("/pump/on", handlePumpOn);
  server.on("/pump/off", handlePumpOff);
  server.on("/motor/on", handleMotorOn);
  server.on("/motor/off", handleMotorOff);
  server.on("/api/waterlevel", handleWaterLevelAPI);
  server.on("/api/status", handleStatusAPI);
  server.on("/debug", handleDebugPage);
  server.onNotFound(handleNotFound);

  // Xử lý routes động cho tốc độ và hướng động cơ
  // Sẽ được xử lý trong handleNotFound để hỗ trợ mọi giá trị từ 0-100

  // Khởi động web server
  server.begin();
  Serial.println("🌐 Web server đã khởi động!");

  if (isWiFiConnected) {
    Serial.println("========================================");
    Serial.println("🎉 SẴN SÀNG SỬ DỤNG!");
    Serial.println("Truy cập bằng một trong các cách sau:");
    Serial.print("   • http://");
    Serial.println(WiFi.localIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  } else if (isAPMode) {
    Serial.println("========================================");
    Serial.println("🎉 ACCESS POINT SẴN SÀNG!");
    Serial.println("Truy cập qua:");
    Serial.print("   • http://");
    Serial.println(WiFi.softAPIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  }
}

void loop() {
  // Xử lý các request từ web server
  server.handleClient();

  // Cập nhật mDNS (hoạt động trong cả WiFi và AP mode)
  MDNS.update();

  // Đọc cảm biến mực nước định kỳ
  if (millis() - lastSensorRead > SENSOR_INTERVAL) {
    lastSensorRead = millis();
    float newLevel = readWaterLevel();

    if (newLevel >= 0) {
      // Chỉ hiển thị khi thay đổi > 0.5cm
      if (abs(newLevel - waterLevel) > 0.5) {
        waterLevel = newLevel;
        Serial.println("💧 Mực nước: " + String(waterLevel, 1) + " cm");
      } else {
        waterLevel = newLevel; // Cập nhật im lặng
      }
    }
  }

  // Xử lý nút nhấn vật lý
  handleButtonPress();

  // Kiểm tra và duy trì kết nối AP nếu đang ở chế độ AP
  if (isAPMode) {
    maintainAPConnection();
  }

  // Kiểm tra và quản lý kết nối WiFi định kỳ
  static unsigned long lastCheck = 0;
  unsigned long checkInterval = isAPMode ? 120000 : 30000; // AP mode: 2 phút, WiFi mode: 30 giây

  if (millis() - lastCheck > checkInterval) {
    lastCheck = millis();
    checkWiFiConnection();
  }
}

// Hàm kiểm tra và quản lý kết nối WiFi
void checkWiFiConnection() {
  if (isWiFiConnected && WiFi.status() != WL_CONNECTED) {
    // Mất kết nối WiFi, chuyển sang AP mode
    Serial.println("⚠️  Mất kết nối WiFi! Chuyển sang chế độ Access Point...");
    isWiFiConnected = false;
    isAPMode = true;

    startStableAP();
    apStartTime = millis();

    Serial.println("✅ Access Point đã được kích hoạt!");
    Serial.println("📶 Kết nối vào WiFi: " + String(ap_ssid));
    Serial.println("🌐 Truy cập: http://" + WiFi.softAPIP().toString());
    Serial.println("🔧 Hoặc qua mDNS: http://" + String(mdns_name) + ".local");
  }
  else if (isAPMode && !isWiFiConnected) {
    // Đang ở chế độ AP, thử kết nối WiFi mà không làm gián đoạn AP

    // Chỉ thử kết nối WiFi mỗi 2 phút để tránh gián đoạn
    if (millis() - lastWiFiRetry > 120000 && !wifiRetryInProgress) {
      lastWiFiRetry = millis();
      wifiRetryInProgress = true;

      Serial.println("🔄 Thử kết nối WiFi (giữ nguyên AP mode)...");

      // Thử kết nối WiFi trong chế độ AP+STA
      WiFi.mode(WIFI_AP_STA);
      delay(100);

      // Khởi tạo lại AP sau khi chuyển mode
      WiFi.softAPConfig(IPAddress(192, 168, 4, 1), IPAddress(192, 168, 4, 1), IPAddress(255, 255, 255, 0));
      WiFi.softAP(ap_ssid, ap_password, ap_channel, ap_hidden, ap_max_connections);

      // Thử kết nối WiFi
      WiFi.begin(ssid, password);

      // Chờ tối đa 10 giây
      int attempts = 0;
      while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;

        // Xử lý web server trong khi chờ
        server.handleClient();
      }

      if (WiFi.status() == WL_CONNECTED) {
        // Kết nối WiFi thành công
        isWiFiConnected = true;
        isAPMode = false;
        Serial.println();
        Serial.println("✅ Đã kết nối WiFi thành công! Tắt AP mode...");
        Serial.println("🌐 IP Address: " + WiFi.localIP().toString());

        // Chuyển về chế độ WiFi thuần túy
        WiFi.mode(WIFI_STA);

        // Khởi tạo lại mDNS cho WiFi mode
        if (MDNS.begin(mdns_name)) {
          Serial.println("✅ mDNS đã được khởi tạo cho WiFi mode!");
          MDNS.addService("http", "tcp", 80);
        }
      } else {
        // Không kết nối được WiFi, giữ nguyên AP mode
        Serial.println();
        Serial.println("❌ Không thể kết nối WiFi, tiếp tục AP mode");

        // Đảm bảo AP vẫn hoạt động
        WiFi.mode(WIFI_AP);
        delay(100);
        startStableAP();
      }

      wifiRetryInProgress = false;
    }
  }
}

// Hàm khởi tạo Access Point ổn định
void startStableAP() {
  Serial.println("🔧 Khởi tạo Access Point ổn định...");

  // Dừng WiFi hiện tại
  WiFi.disconnect();
  WiFi.mode(WIFI_OFF);
  delay(100);

  // Cấu hình AP với các tham số ổn định
  WiFi.mode(WIFI_AP);
  delay(100);

  // Cấu hình IP tĩnh cho AP
  IPAddress apIP(192, 168, 4, 1);
  IPAddress gateway(192, 168, 4, 1);
  IPAddress subnet(255, 255, 255, 0);

  WiFi.softAPConfig(apIP, gateway, subnet);

  // Khởi tạo AP với cấu hình nâng cao
  bool apResult = WiFi.softAP(ap_ssid, ap_password, ap_channel, ap_hidden, ap_max_connections);

  if (apResult) {
    Serial.println("✅ AP khởi tạo thành công!");
    Serial.println("📊 Cấu hình AP:");
    Serial.println("   🏷️  SSID: " + String(ap_ssid));
    Serial.println("   🔐 Password: " + String(ap_password));
    Serial.println("   📡 Channel: " + String(ap_channel));
    Serial.println("   👥 Max Connections: " + String(ap_max_connections));
    Serial.println("   🌐 IP: " + WiFi.softAPIP().toString());

    // Khởi tạo mDNS cho AP
    delay(500); // Chờ AP ổn định
    if (MDNS.begin(mdns_name)) {
      MDNS.addService("http", "tcp", 80);
      Serial.println("✅ mDNS đã khởi tạo cho AP mode!");
    } else {
      Serial.println("⚠️  mDNS không khởi tạo được cho AP mode");
    }
  } else {
    Serial.println("❌ Lỗi khởi tạo AP!");
  }
}

// Hàm duy trì kết nối AP ổn định
void maintainAPConnection() {
  // Kiểm tra mỗi 10 giây (giảm tần suất để ổn định hơn)
  if (millis() - lastAPCheck > 10000) {
    lastAPCheck = millis();

    // Bỏ qua kiểm tra nếu đang thử kết nối WiFi
    if (wifiRetryInProgress) {
      return;
    }

    // Kiểm tra trạng thái AP
    wifi_softap_get_station_num(); // Refresh station count
    int connectedClients = WiFi.softAPgetStationNum();

    // Debug thông tin AP (chỉ khi có thay đổi)
    static int lastClientCount = -1;
    static unsigned long lastClientReport = 0;

    if (connectedClients != lastClientCount || millis() - lastClientReport > 60000) {
      Serial.println("📱 AP Status - Clients: " + String(connectedClients) +
                    ", Uptime: " + String((millis() - apStartTime) / 1000) + "s");
      lastClientCount = connectedClients;
      lastClientReport = millis();
    }

    // Kiểm tra nếu AP bị lỗi (không có IP hoặc không hoạt động)
    IPAddress apIP = WiFi.softAPIP();
    if (apIP == IPAddress(0, 0, 0, 0)) {
      Serial.println("⚠️  AP mất IP, khởi tạo lại...");
      startStableAP();
      apStartTime = millis();
      return;
    }

    // Chỉ restart AP nếu thực sự cần thiết (6 giờ không có client)
    unsigned long apUptime = millis() - apStartTime;
    if (apUptime > 21600000 && connectedClients == 0) { // 6 giờ không có client
      Serial.println("🔄 Restart AP sau 6 giờ không có client...");
      startStableAP();
      apStartTime = millis();
    }
  }
}
