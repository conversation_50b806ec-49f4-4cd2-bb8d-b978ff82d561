# 🔧 Hướng dẫn Debug Hệ thống Motor Control

## 🚨 Vấn đề hiện tại
Các nút trên web interface không hoạt động - có thể bấm được nhưng chức năng không thực thi.

## 🔍 Các bước kiểm tra lỗi

### 1. Ki<PERSON>m tra Serial Monitor
```bash
pio device monitor
```

**Tìm kiếm các log sau:**
- `🔧 GPIO khởi tạo` - Kiểm tra GPIO được setup đúng
- `🔧 Motor pins: ENA=16, IN1=0, IN2=2` - Xác nhận pin mapping
- `🔧 API: /motor/on called` - Kiểm tra API được gọi
- `🔧 controlMotor called` - Xác nhận hàm được thực thi

### 2. Test API trực tiếp
Mở browser và test các URL sau:

```
http://waterlevel.local/api/status
http://waterlevel.local/motor/on
http://waterlevel.local/motor/off
http://waterlevel.local/motor/speed/50
http://waterlevel.local/motor/direction/1
```

**Kết quả mong đợi:**
- `/api/status` → JSON với motor status
- `/motor/on` → "MOTOR ON - Speed: XX%, Direction: X"
- `/motor/off` → "MOTOR OFF"
- `/motor/speed/50` → "SPEED SET: 50%"
- `/motor/direction/1` → "DIRECTION SET: FORWARD"

### 3. Sử dụng Debug Page
1. Copy file `debug_motor.html` vào thư mục web server
2. Truy cập `http://waterlevel.local/debug_motor.html`
3. Sử dụng các nút test để kiểm tra từng API

### 4. Kiểm tra JavaScript Console
1. Mở Developer Tools (F12)
2. Vào tab Console
3. Bấm các nút trên web interface
4. Tìm lỗi JavaScript:
   - `Uncaught ReferenceError` - Hàm không được định nghĩa
   - `Failed to fetch` - Lỗi kết nối API
   - `SyntaxError` - Lỗi cú pháp

## 🔧 Các lỗi thường gặp và cách sửa

### Lỗi 1: API không phản hồi
**Triệu chứng:** Nút bấm được nhưng không có phản ứng
**Nguyên nhân:** Route không được đăng ký hoặc handler bị lỗi
**Cách sửa:**
```cpp
// Kiểm tra trong setup()
server.on("/motor/on", handleMotorOn);
server.on("/motor/off", handleMotorOff);
```

### Lỗi 2: JavaScript function không tồn tại
**Triệu chứng:** Console báo "function is not defined"
**Nguyên nhân:** HTML không chứa JavaScript hoặc có lỗi cú pháp
**Cách sửa:** Kiểm tra phần `<script>` trong HTML

### Lỗi 3: GPIO không hoạt động
**Triệu chứng:** API hoạt động nhưng motor không quay
**Nguyên nhân:** 
- Sai pin mapping
- Không có nguồn cho L298N
- Kết nối dây sai
**Cách sửa:** Kiểm tra kết nối phần cứng

### Lỗi 4: PWM không hoạt động
**Triệu chứng:** Motor chỉ ON/OFF, không điều chỉnh được tốc độ
**Nguyên nhân:** Pin ENA không hỗ trợ PWM hoặc sai cách sử dụng
**Cách sửa:** 
```cpp
// Đảm bảo sử dụng pin hỗ trợ PWM
const int MOTOR_ENA = 16;  // D0 - hỗ trợ PWM
analogWrite(MOTOR_ENA, pwmValue);
```

## 🧪 Test Cases

### Test 1: Basic API
```bash
curl http://waterlevel.local/api/status
# Expected: {"level":X,"pump":false,"motor":false,"motorSpeed":0,"motorDirection":1}
```

### Test 2: Motor Control
```bash
curl http://waterlevel.local/motor/on
curl http://waterlevel.local/motor/speed/75
curl http://waterlevel.local/motor/direction/-1
curl http://waterlevel.local/motor/off
```

### Test 3: JavaScript Functions
Mở Console và chạy:
```javascript
controlMotor('on');
setMotorSpeed(50);
setDirection(1);
updateStatus();
```

## 📊 Debug Checklist

- [ ] Serial Monitor hiển thị log khởi tạo GPIO
- [ ] API `/api/status` trả về JSON hợp lệ
- [ ] API `/motor/on` trả về "MOTOR ON"
- [ ] JavaScript Console không có lỗi
- [ ] Các hàm JS được định nghĩa: `controlMotor`, `setMotorSpeed`, `setDirection`
- [ ] Kết nối phần cứng L298N đúng
- [ ] Nguồn điện L298N đủ mạnh (6-12V)
- [ ] Pin GPIO mapping đúng: ENA=D0, IN1=D3, IN2=D4

## 🔄 Quy trình Debug

1. **Upload code mới** với debug logging
2. **Mở Serial Monitor** để theo dõi log
3. **Test API trực tiếp** qua browser
4. **Kiểm tra JavaScript Console** khi bấm nút
5. **Sử dụng debug page** để test từng chức năng
6. **Kiểm tra phần cứng** nếu API hoạt động nhưng motor không quay

## 📞 Báo cáo lỗi

Khi báo cáo lỗi, vui lòng cung cấp:
1. **Serial Monitor log** khi bấm nút
2. **JavaScript Console errors** (nếu có)
3. **API response** khi test trực tiếp
4. **Sơ đồ kết nối** phần cứng
5. **Thông số nguồn điện** đang sử dụng

---
**Lưu ý:** Code hiện tại đã được thêm debug logging chi tiết. Hãy upload và kiểm tra Serial Monitor để xác định chính xác vấn đề.
