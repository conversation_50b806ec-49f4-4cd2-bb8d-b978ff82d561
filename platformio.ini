; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp12e]
platform = espressif8266
board = esp12e
framework = arduino
monitor_speed = 115200
upload_speed = 115200
upload_resetmethod = dtr
# upload_port = /dev/cu.usbserial-0001
lib_deps =
    adafruit/Adafruit_VL53L0X@^1.2.2