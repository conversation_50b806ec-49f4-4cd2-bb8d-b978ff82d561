# Hệ Thống Đo Mực Nước & Điều Khiển Động Cơ ESP8266

Hệ thống IoT hoàn chỉnh sử dụng ESP8266 để đo mực nước bằng cảm biến VL53L0X và điều khiển bơm nước + động cơ DC qua giao diện web.

## ✨ Tính năng chính

### 📏 Đo mực nước
- Cảm biến laser VL53L0X độ chính cao
- Hiển thị mực nước real-time
- Giao diện trực quan với thanh mực nước

### 🚰 Điều khiển bơm nước
- Bật/tắt bơm qua web hoặc nút vật lý
- Chế độ nhấn giữ để bơm
- Không giới hạn thời gian bơm

### ⚙️ Điều khiển động cơ DC (MỚI!)
- Đ<PERSON>ều khiển tốc độ 0-100% qua PWM
- Đổi hướng quay: thuận/nghịch
- Giao diện slider trực quan
- API RESTful đầy đủ

### 🌐 Kết nối mạng thông minh
- Tự động kết nối WiFi
- Fallback sang Access Point nếu mất WiFi
- mDNS: truy cập qua `http://waterlevel.local`
- Tự động thử kết nối lại WiFi

## 🔧 Phần cứng cần thiết

### ESP8266 NodeMCU
- Vi điều khiển chính
- WiFi tích hợp

### Cảm biến VL53L0X
- Đo khoảng cách laser
- Kết nối I2C (SDA=D2, SCL=D1)

### L298N Motor Driver (MỚI!)
- Điều khiển động cơ DC
- Hỗ trợ PWM và đổi hướng
- Xem chi tiết: [MOTOR_WIRING.md](MOTOR_WIRING.md)

### Relay Module
- Điều khiển bơm nước
- Kết nối GPIO14 (D5)

### Nút nhấn
- Điều khiển bơm vật lý
- Kết nối GPIO12 (D6)

## 📋 Sơ đồ kết nối

```
ESP8266 NodeMCU:
├── VL53L0X (I2C)
│   ├── VCC → 3.3V
│   ├── GND → GND
│   ├── SDA → D2 (GPIO4)
│   └── SCL → D1 (GPIO5)
├── Relay Bơm
│   ├── VCC → 5V
│   ├── GND → GND
│   └── IN  → D5 (GPIO14)
├── L298N Motor Driver
│   ├── ENA → D0 (GPIO16) - PWM
│   ├── IN1 → D3 (GPIO0)  - Direction
│   ├── IN2 → D4 (GPIO2)  - Direction
│   └── GND → GND
└── Nút nhấn
    ├── Pin 1 → D6 (GPIO12)
    └── Pin 2 → GND
```

## 🚀 Cài đặt và sử dụng

### 1. Cấu hình WiFi
Chỉnh sửa trong `src/main.cpp`:
```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";
const char* password = "MAT_KHAU_WIFI";
```

### 2. Build và Upload
```bash
pio run --target upload
```

### 3. Truy cập giao diện
- **WiFi mode**: `http://waterlevel.local` hoặc IP được cấp
- **AP mode**: Kết nối WiFi `ESP8266-WaterLevel` → `http://***********`

## 🎮 Cách sử dụng

### Giao diện Web
1. **Mực nước**: Hiển thị tự động, cập nhật mỗi 2 giây
2. **Bơm nước**: Nhấn giữ để bơm, nhả để dừng
3. **Động cơ DC**:
   - Điều chỉnh tốc độ bằng slider (0-100%)
   - Chọn hướng: Thuận ↻ hoặc Nghịch ↺
   - Bật/tắt động cơ

### API Commands
```bash
# Bơm nước
GET /pump/on          # Bật bơm
GET /pump/off         # Tắt bơm

# Động cơ DC
GET /motor/on         # Bật động cơ
GET /motor/off        # Tắt động cơ
GET /motor/speed/75   # Tốc độ 75%
GET /motor/direction/1   # Thuận (1) hoặc nghịch (-1)

# Trạng thái
GET /api/status       # Trạng thái tổng thể (JSON)
GET /api/waterlevel   # Chỉ mực nước (JSON)
```

## 📱 Tính năng nâng cao

### Responsive Design
- Tối ưu cho mobile và desktop
- Dark theme chuyên nghiệp
- Giao diện trực quan

### Tự động kết nối
- Thử kết nối WiFi 20 giây
- Tự động chuyển AP mode nếu thất bại
- Thử kết nối lại WiFi mỗi 2 phút

### An toàn vận hành
- Tốc độ động cơ tối thiểu 30%
- Debounce nút nhấn vật lý
- Xử lý lỗi cảm biến

## 🔍 Troubleshooting

### Không kết nối được WiFi
1. Kiểm tra tên WiFi và mật khẩu
2. ESP8266 sẽ tự động tạo AP: `ESP8266-WaterLevel`
3. Kết nối AP và truy cập `http://***********`

### Cảm biến không hoạt động
1. Kiểm tra kết nối I2C (SDA, SCL)
2. Đảm bảo nguồn 3.3V ổn định
3. Xem Serial Monitor để debug

### Động cơ không quay
1. Kiểm tra kết nối L298N
2. Đảm bảo nguồn riêng cho L298N (6-12V)
3. Tốc độ phải ≥ 30%
4. Xem chi tiết: [MOTOR_WIRING.md](MOTOR_WIRING.md)

## 📊 Thông số kỹ thuật

- **Vi điều khiển**: ESP8266 (80MHz, 4MB Flash)
- **Cảm biến**: VL53L0X (I2C, 2m range)
- **Motor Driver**: L298N (2A per channel)
- **Nguồn**: 5V USB + 6-12V cho động cơ
- **WiFi**: 802.11 b/g/n
- **Web Server**: ESP8266WebServer
- **mDNS**: Multicast DNS

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng tạo Issue hoặc Pull Request.

---
**Phát triển bởi**: ESP8266 Water Level & Motor Control System
**Phiên bản**: 2.0 (Thêm điều khiển động cơ DC)