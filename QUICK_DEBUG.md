# 🚀 Quick Debug - Motor Control Issues

## ⚡ Các bước debug nhanh

### 1. Upload code mới (có debug logging)
```bash
pio run --target upload
```

### 2. Mở Serial Monitor
```bash
pio device monitor
```

### 3. <PERSON><PERSON><PERSON> tra khởi tạo
Tìm trong Serial Monitor:
```
✅ GPIO khởi tạo: Bơm=TẮT, Động cơ=TẮT, Nút=INPUT_PULLUP
🔧 Motor pins: ENA=16, IN1=0, IN2=2
```

### 4. Test API trực tiếp
Mở browser và test:
- `http://waterlevel.local/debug` - Trang debug đơn giản
- `http://waterlevel.local/api/status` - Kiểm tra JSON response
- `http://waterlevel.local/motor/on` - Test bật motor

### 5. Kiểm tra JavaScript Console
1. Mở trang chính: `http://waterlevel.local`
2. Nhấn F12 → Console tab
3. Bấm nút "BẬT ĐỘNG CƠ"
4. <PERSON>em có lỗi JavaScript không

## 🔍 Các lỗi thường gặp

### ❌ Lỗi: "function is not defined"
**Nguyên nhân:** JavaScript không được load đúng
**Cách sửa:** Kiểm tra HTML có chứa đầy đủ `<script>` section

### ❌ Lỗi: "Failed to fetch"
**Nguyên nhân:** API endpoint không tồn tại
**Cách sửa:** Kiểm tra routes được đăng ký trong `setup()`

### ❌ Lỗi: Motor không quay
**Nguyên nhân:** 
- Không có nguồn cho L298N
- Kết nối dây sai
- GPIO không được setup đúng

## 🧪 Test Commands

### Test bằng curl (nếu có)
```bash
curl http://waterlevel.local/api/status
curl http://waterlevel.local/motor/on
curl http://waterlevel.local/motor/speed/50
```

### Test bằng Python script
```bash
python3 test_api.py
```

## 📊 Expected Serial Output

Khi bấm nút "BẬT ĐỘNG CƠ", bạn sẽ thấy:
```
🔧 API: /motor/on called
🔧 Using speed: 30%, direction: 1
🔧 controlMotor called: state=1, speed=30, direction=1
🔧 PWM value: 307 (from 30%)
🔧 Direction: FORWARD (IN1=HIGH, IN2=LOW)
🔧 PWM written to pin 16
⚙️ Động cơ: BẬT - Tốc độ: 30% - Hướng: Thuận
```

## 🔧 Hardware Checklist

- [ ] L298N có nguồn riêng 6-12V
- [ ] GND chung giữa ESP8266 và L298N
- [ ] ENA → D0 (GPIO16)
- [ ] IN1 → D3 (GPIO0)  
- [ ] IN2 → D4 (GPIO2)
- [ ] OUT1, OUT2 → Motor DC

## 📞 Nếu vẫn lỗi

1. **Copy toàn bộ Serial Monitor output** khi bấm nút
2. **Screenshot JavaScript Console errors**
3. **Test API trực tiếp** và báo cáo kết quả
4. **Kiểm tra kết nối phần cứng** theo sơ đồ

---
**Lưu ý:** Code hiện tại có debug logging chi tiết. Hãy upload và kiểm tra Serial Monitor để tìm nguyên nhân chính xác.
