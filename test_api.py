#!/usr/bin/env python3
"""
Script test API cho ESP8266 Motor Control System
Sử dụng để kiểm tra các endpoint API hoạt động đúng không
"""

import requests
import json
import time
import sys

# C<PERSON>u hình
ESP_IP = "waterlevel.local"  # Hoặc thay bằng IP cụ thể như "*************"
BASE_URL = f"http://{ESP_IP}"

def test_api(endpoint, expected_status=200, description=""):
    """Test một API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔧 Testing: {endpoint}")
    print(f"📝 Description: {description}")
    print(f"🌐 URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"✅ Status: {response.status_code}")
        print(f"📄 Response: {response.text[:200]}...")
        
        if response.status_code == expected_status:
            print("✅ PASS")
            return True
        else:
            print(f"❌ FAIL - Expected {expected_status}, got {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: {e}")
        return False

def test_json_api(endpoint, description=""):
    """Test API endpoint trả về JSON"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔧 Testing JSON: {endpoint}")
    print(f"📝 Description: {description}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📊 JSON Data:")
                for key, value in data.items():
                    print(f"   {key}: {value}")
                print("✅ PASS - Valid JSON")
                return True
            except json.JSONDecodeError:
                print(f"❌ FAIL - Invalid JSON: {response.text}")
                return False
        else:
            print(f"❌ FAIL - Status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    print("🚀 ESP8266 Motor Control API Test")
    print("=" * 50)
    
    # Test kết nối cơ bản
    print(f"\n📡 Testing connection to {ESP_IP}...")
    if not test_api("/", description="Main page"):
        print(f"❌ Cannot connect to {ESP_IP}")
        print("💡 Suggestions:")
        print("   - Check ESP8266 is powered on")
        print("   - Check WiFi connection")
        print("   - Try IP address instead of hostname")
        print("   - Check firewall settings")
        return False
    
    # Test API endpoints
    tests = [
        # Status APIs
        ("/api/status", "Get system status (JSON)"),
        ("/api/waterlevel", "Get water level (JSON)"),
        
        # Motor control
        ("/motor/on", "Turn motor ON"),
        ("/motor/off", "Turn motor OFF"),
        
        # Speed control
        ("/motor/speed/0", "Set speed to 0%"),
        ("/motor/speed/30", "Set speed to 30%"),
        ("/motor/speed/50", "Set speed to 50%"),
        ("/motor/speed/75", "Set speed to 75%"),
        ("/motor/speed/100", "Set speed to 100%"),
        
        # Direction control
        ("/motor/direction/1", "Set direction FORWARD"),
        ("/motor/direction/-1", "Set direction REVERSE"),
        
        # Pump control
        ("/pump/on", "Turn pump ON"),
        ("/pump/off", "Turn pump OFF"),
    ]
    
    passed = 0
    total = len(tests)
    
    for endpoint, description in tests:
        if endpoint.startswith("/api/"):
            success = test_json_api(endpoint, description)
        else:
            success = test_api(endpoint, description=description)
        
        if success:
            passed += 1
        
        time.sleep(0.5)  # Delay giữa các test
    
    # Test sequence - Motor demo
    print(f"\n🎬 Running motor demo sequence...")
    demo_steps = [
        ("/motor/speed/50", "Set speed 50%"),
        ("/motor/direction/1", "Set forward direction"),
        ("/motor/on", "Start motor"),
        ("/api/status", "Check status"),
        ("/motor/speed/75", "Increase speed to 75%"),
        ("/motor/direction/-1", "Reverse direction"),
        ("/motor/off", "Stop motor"),
    ]
    
    for endpoint, description in demo_steps:
        print(f"\n⏳ {description}...")
        if endpoint.startswith("/api/"):
            test_json_api(endpoint, description)
        else:
            test_api(endpoint, description=description)
        time.sleep(1)
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ API is working correctly")
    else:
        print(f"\n⚠️  {total - passed} TESTS FAILED")
        print("💡 Check Serial Monitor for detailed logs")
        print("💡 Verify hardware connections")
        print("💡 Check power supply for L298N")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
