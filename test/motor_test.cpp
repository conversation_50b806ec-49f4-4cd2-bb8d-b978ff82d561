/*
 * Test chức năng điều khiển động cơ DC với L298N
 * File này chứa các test case để kiểm tra hoạt động của động cơ
 */

#include <Arduino.h>
#include <unity.h>

// Đ<PERSON>nh nghĩa chân GPIO (giống như trong main.cpp)
const int MOTOR_ENA = 16;  // GPIO16 (D0) - PWM Enable A
const int MOTOR_IN1 = 0;   // GPIO0 (D3) - Input 1
const int MOTOR_IN2 = 2;   // GPIO2 (D4) - Input 2

// Biến test
bool motorState = false;
int motorSpeed = 0;
int motorDirection = 1;
const int MIN_MOTOR_SPEED = 30;
const int MAX_MOTOR_SPEED = 100;

// Hàm điều khiển động cơ (copy từ main.cpp)
void controlMotor(bool state, int speed, int direction) {
  motorState = state;
  
  if (state) {
    if (speed < MIN_MOTOR_SPEED) speed = MIN_MOTOR_SPEED;
    if (speed > MAX_MOTOR_SPEED) speed = MAX_MOTOR_SPEED;
    
    motorSpeed = speed;
    motorDirection = direction;
    
    int pwmValue = map(speed, 0, 100, 0, 1023);
    
    if (direction == 1) {
      digitalWrite(MOTOR_IN1, HIGH);
      digitalWrite(MOTOR_IN2, LOW);
    } else {
      digitalWrite(MOTOR_IN1, LOW);
      digitalWrite(MOTOR_IN2, HIGH);
    }
    
    analogWrite(MOTOR_ENA, pwmValue);
  } else {
    motorSpeed = 0;
    digitalWrite(MOTOR_IN1, LOW);
    digitalWrite(MOTOR_IN2, LOW);
    analogWrite(MOTOR_ENA, 0);
  }
}

void setMotorSpeed(int speed) {
  if (speed < 0) speed = 0;
  if (speed > MAX_MOTOR_SPEED) speed = MAX_MOTOR_SPEED;
  
  motorSpeed = speed;
  
  if (motorState && speed >= MIN_MOTOR_SPEED) {
    int pwmValue = map(speed, 0, 100, 0, 1023);
    analogWrite(MOTOR_ENA, pwmValue);
  } else if (motorState && speed < MIN_MOTOR_SPEED) {
    controlMotor(false, 0, motorDirection);
  }
}

void setMotorDirection(int direction) {
  motorDirection = direction;
  
  if (motorState) {
    if (direction == 1) {
      digitalWrite(MOTOR_IN1, HIGH);
      digitalWrite(MOTOR_IN2, LOW);
    } else {
      digitalWrite(MOTOR_IN1, LOW);
      digitalWrite(MOTOR_IN2, HIGH);
    }
  }
}

// Test cases
void test_motor_initialization() {
  // Test khởi tạo GPIO
  pinMode(MOTOR_ENA, OUTPUT);
  pinMode(MOTOR_IN1, OUTPUT);
  pinMode(MOTOR_IN2, OUTPUT);
  
  digitalWrite(MOTOR_IN1, LOW);
  digitalWrite(MOTOR_IN2, LOW);
  analogWrite(MOTOR_ENA, 0);
  
  TEST_ASSERT_EQUAL(false, motorState);
  TEST_ASSERT_EQUAL(0, motorSpeed);
  TEST_ASSERT_EQUAL(1, motorDirection);
}

void test_motor_start_stop() {
  // Test bật/tắt động cơ
  controlMotor(true, 50, 1);
  TEST_ASSERT_EQUAL(true, motorState);
  TEST_ASSERT_EQUAL(50, motorSpeed);
  TEST_ASSERT_EQUAL(1, motorDirection);
  
  controlMotor(false, 0, 1);
  TEST_ASSERT_EQUAL(false, motorState);
  TEST_ASSERT_EQUAL(0, motorSpeed);
}

void test_motor_speed_limits() {
  // Test giới hạn tốc độ
  controlMotor(true, 10, 1);  // Dưới MIN_MOTOR_SPEED
  TEST_ASSERT_EQUAL(MIN_MOTOR_SPEED, motorSpeed);
  
  controlMotor(true, 150, 1); // Trên MAX_MOTOR_SPEED
  TEST_ASSERT_EQUAL(MAX_MOTOR_SPEED, motorSpeed);
  
  controlMotor(true, 75, 1);  // Trong khoảng hợp lệ
  TEST_ASSERT_EQUAL(75, motorSpeed);
}

void test_motor_direction() {
  // Test hướng quay
  controlMotor(true, 50, 1);
  TEST_ASSERT_EQUAL(1, motorDirection);
  
  setMotorDirection(-1);
  TEST_ASSERT_EQUAL(-1, motorDirection);
  
  setMotorDirection(1);
  TEST_ASSERT_EQUAL(1, motorDirection);
}

void test_motor_speed_adjustment() {
  // Test điều chỉnh tốc độ khi đang chạy
  controlMotor(true, 50, 1);
  
  setMotorSpeed(75);
  TEST_ASSERT_EQUAL(75, motorSpeed);
  TEST_ASSERT_EQUAL(true, motorState);
  
  setMotorSpeed(20); // Dưới MIN_MOTOR_SPEED, should stop
  TEST_ASSERT_EQUAL(false, motorState);
}

void test_pwm_mapping() {
  // Test mapping tốc độ sang PWM
  int speed50 = map(50, 0, 100, 0, 1023);
  int speed100 = map(100, 0, 100, 0, 1023);
  
  TEST_ASSERT_EQUAL(511, speed50);   // 50% = 511/1023
  TEST_ASSERT_EQUAL(1023, speed100); // 100% = 1023/1023
}

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  UNITY_BEGIN();
  
  RUN_TEST(test_motor_initialization);
  RUN_TEST(test_motor_start_stop);
  RUN_TEST(test_motor_speed_limits);
  RUN_TEST(test_motor_direction);
  RUN_TEST(test_motor_speed_adjustment);
  RUN_TEST(test_pwm_mapping);
  
  UNITY_END();
}

void loop() {
  // Demo sequence - chạy sau khi test xong
  static unsigned long lastDemo = 0;
  static int demoStep = 0;
  
  if (millis() - lastDemo > 3000) { // Mỗi 3 giây
    lastDemo = millis();
    
    switch(demoStep) {
      case 0:
        Serial.println("Demo: Bật động cơ 50% thuận");
        controlMotor(true, 50, 1);
        break;
      case 1:
        Serial.println("Demo: Tăng tốc độ lên 80%");
        setMotorSpeed(80);
        break;
      case 2:
        Serial.println("Demo: Đổi hướng nghịch");
        setMotorDirection(-1);
        break;
      case 3:
        Serial.println("Demo: Giảm tốc độ xuống 40%");
        setMotorSpeed(40);
        break;
      case 4:
        Serial.println("Demo: Tắt động cơ");
        controlMotor(false, 0, 1);
        break;
      default:
        demoStep = -1; // Reset
        break;
    }
    
    demoStep++;
  }
}
